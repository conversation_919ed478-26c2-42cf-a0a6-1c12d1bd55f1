# hardware_info.nim
# Demonstrates direct sysctl calls to access low-level hardware information
# Compile with: nim c -d:release hardware_info.nim
# Uses BSD sysctl interface for hardware info

import strutils

# sysctl for hardware info
proc sysctlbyname(name: cstring, oldp: pointer, oldlenp: ptr int, newp: pointer, newlen: int): cint {.importc, header: "<sys/sysctl.h>".}
proc sysctl(name: ptr cint, namelen: cuint, oldp: pointer, oldlenp: ptr int, newp: pointer, newlen: int): cint {.importc, header: "<sys/sysctl.h>".}

# sysctl constants
const
  CTL_HW = 6
  HW_MODEL = 2
  HW_MACHINE = 4
  HW_NCPU = 3
  HW_PHYSMEM = 5
  HW_USERMEM = 6

# Helper function to get sysctl string value
proc getSysctlString(name: string): string =
  var size: int = 0
  # First call to get the size
  if sysctlbyname(name.cstring, nil, addr size, nil, 0) != 0:
    return "Unknown"

  # Allocate buffer and get the actual value
  let buffer = alloc(size)
  if sysctlbyname(name.cstring, buffer, addr size, nil, 0) == 0:
    result = $cast[cstring](buffer)
  else:
    result = "Unknown"

  dealloc(buffer)

# Helper function to get sysctl integer value
proc getSysctlInt(name: string): int =
  var value: int = 0
  var size = sizeof(int)
  if sysctlbyname(name.cstring, addr value, addr size, nil, 0) == 0:
    return value
  else:
    return -1

# Helper function to get sysctl uint64 value
proc getSysctlUint64(name: string): uint64 =
  var value: uint64 = 0
  var size = sizeof(uint64)
  if sysctlbyname(name.cstring, addr value, addr size, nil, 0) == 0:
    return value
  else:
    return 0

proc getHardwareInfo(): tuple[model: string, machine: string, cpuBrand: string, cpuCount: int, memorySize: string, kernelVersion: string] =
  ## Retrieves hardware information using sysctl

  # Get hardware model
  let model = getSysctlString("hw.model")

  # Get machine type
  let machine = getSysctlString("hw.machine")

  # Get CPU brand
  let cpuBrand = getSysctlString("machdep.cpu.brand_string")

  # Get CPU count
  let cpuCount = getSysctlInt("hw.ncpu")

  # Get memory size
  let memBytes = getSysctlUint64("hw.memsize")
  let memorySize = if memBytes > 0: $(memBytes div (1024 * 1024 * 1024)) & " GB" else: "Unknown"

  # Get kernel version
  let kernelVersion = getSysctlString("kern.version")

  return (model, machine, cpuBrand, cpuCount, memorySize, kernelVersion)

proc main() =
  echo "=== macOS Hardware Information Demo ==="
  echo "Using direct sysctl calls to access hardware info"
  echo ""

  let hwInfo = getHardwareInfo()

  echo "Hardware Model: ", hwInfo.model
  echo "Machine Type: ", hwInfo.machine
  echo "CPU Brand: ", hwInfo.cpuBrand
  echo "CPU Count: ", hwInfo.cpuCount
  echo "Memory Size: ", hwInfo.memorySize
  echo "Kernel Version: ", hwInfo.kernelVersion.split('\n')[0]  # First line only
  echo ""
  echo "Technical details:"
  echo "- Used sysctl() system calls for hardware info"
  echo "- Direct BSD kernel interface access"
  echo "- No framework dependencies"
  echo "- Raw C function calls via FFI"

when isMainModule:
  main()
