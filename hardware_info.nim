# hardware_info.nim
# Demonstrates direct IOKit calls to access low-level hardware information
# Compile with: nim c -d:release hardware_info.nim
# Required frameworks: IOKit, CoreFoundation

# IOKit and CoreFoundation types
type
  io_object_t = uint32
  io_iterator_t = io_object_t
  io_registry_entry_t = io_object_t
  kern_return_t = int32
  mach_port_t = uint32
  CFStringRef = pointer
  CFMutableDictionaryRef = pointer
  CFTypeRef = pointer
  CFStringEncoding = uint32

const
  kCFStringEncodingUTF8: CFStringEncoding = 0x08000100
  KERN_SUCCESS: kern_return_t = 0
  kIOServicePlane = "IOService"

# Framework linking
{.passL: "-framework IOKit".}
{.passL: "-framework CoreFoundation".}

# CoreFoundation functions
proc CFStringCreateWithCString(allocator: pointer, cStr: cstring, encoding: CFStringEncoding): CFStringRef {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFStringGetCStringPtr(theString: CFStringRef, encoding: CFStringEncoding): cstring {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFStringGetCString(theString: CFStringRef, buffer: cstring, bufferSize: int, encoding: CFStringEncoding): bool {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFStringGetLength(theString: CFStringRef): int {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFRelease(cf: CFTypeRef) {.importc, header: "<CoreFoundation/CoreFoundation.h>".}

# IOKit functions
proc IOServiceMatching(name: cstring): CFMutableDictionaryRef {.importc, header: "<IOKit/IOKitLib.h>".}
proc IOServiceGetMatchingServices(mainPort: mach_port_t,
                                  matching: CFMutableDictionaryRef,
                                  existing: ptr io_iterator_t): kern_return_t {.importc, header: "<IOKit/IOKitLib.h>".}
proc IOIteratorNext(iterator: io_iterator_t): io_object_t {.importc, header: "<IOKit/IOKitLib.h>".}
proc IORegistryEntryCreateCFProperty(entry: io_registry_entry_t,
                                      key: CFStringRef,
                                      allocator: pointer,
                                      options: uint32): CFTypeRef {.importc, header: "<IOKit/IOKitLib.h>".}
proc IOObjectRelease(obj: io_object_t): kern_return_t {.importc, header: "<IOKit/IOKitLib.h>".}
# Use kIOMainPortDefault constant instead of IOMasterPort
var kIOMainPortDefault {.importc, header: "<IOKit/IOKitLib.h>".}: mach_port_t

# Helper function to convert CFStringRef to Nim string
proc cfStringToNimString(cfStr: CFStringRef): string =
  if cfStr == nil:
    return ""

  # Try to get direct pointer first (more efficient)
  let directPtr = CFStringGetCStringPtr(cfStr, kCFStringEncodingUTF8)
  if directPtr != nil:
    return $directPtr

  # Fall back to copying into buffer
  let length = CFStringGetLength(cfStr)
  let bufferSize = length * 4 + 1  # UTF-8 can be up to 4 bytes per character
  let buffer = cast[cstring](alloc(bufferSize))

  if CFStringGetCString(cfStr, buffer, bufferSize, kCFStringEncodingUTF8):
    result = $buffer
  else:
    result = ""

  dealloc(buffer)

proc getIOKitProperty(serviceName: string, propertyName: string): string =
  ## Gets a property from an IOKit service
  let matching = IOServiceMatching(serviceName.cstring)
  if matching == nil:
    return "Error: Could not create matching dictionary"

  var iterator: io_iterator_t
  if IOServiceGetMatchingServices(kIOMainPortDefault, matching, addr iterator) != KERN_SUCCESS:
    return "Error: Could not get matching services"

  let service = IOIteratorNext(iterator)
  if service == 0:
    discard IOObjectRelease(iterator)
    return "Error: No matching service found"

  let propertyKey = CFStringCreateWithCString(nil, propertyName.cstring, kCFStringEncodingUTF8)
  let property = IORegistryEntryCreateCFProperty(service, propertyKey, nil, 0)

  var result = "Unknown"
  if property != nil:
    result = cfStringToNimString(cast[CFStringRef](property))
    CFRelease(property)

  CFRelease(propertyKey)
  discard IOObjectRelease(service)
  discard IOObjectRelease(iterator)

  return result

proc getHardwareInfo(): tuple[platformUUID: string, serialNumber: string, modelName: string, boardId: string] =
  ## Retrieves hardware information using IOKit

  # Get platform UUID from IOPlatformExpertDevice
  let platformUUID = getIOKitProperty("IOPlatformExpertDevice", "IOPlatformUUID")

  # Get serial number
  let serialNumber = getIOKitProperty("IOPlatformExpertDevice", "IOPlatformSerialNumber")

  # Get model name
  let modelName = getIOKitProperty("IOPlatformExpertDevice", "model")

  # Get board ID
  let boardId = getIOKitProperty("IOPlatformExpertDevice", "board-id")

  return (platformUUID, serialNumber, modelName, boardId)

proc main() =
  echo "=== macOS IOKit Hardware Information Demo ==="
  echo "Using direct IOKit calls to access hardware registry"
  echo ""

  let hwInfo = getHardwareInfo()

  echo "Platform UUID: ", hwInfo.platformUUID
  echo "Serial Number: ", hwInfo.serialNumber
  echo "Model Name: ", hwInfo.modelName
  echo "Board ID: ", hwInfo.boardId
  echo ""
  echo "Technical details:"
  echo "- Used IOKit framework for hardware registry access"
  echo "- Direct IOServiceMatching and IORegistryEntry calls"
  echo "- Accessed IOPlatformExpertDevice service"
  echo "- Manual memory management with IOObjectRelease"
  echo "- Framework: IOKit, CoreFoundation"

when isMainModule:
  main()
