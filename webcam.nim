# webcam.nim
# Demonstrates native macOS webcam capture using system APIs
# Compile with: nim c -d:release webcam.nim
# Uses native imagesnap command and system calls

import times, os

# System call types and functions
type
  pid_t = int32

# System functions
proc system(command: cstring): cint {.importc, header: "<stdlib.h>".}
proc getpid(): pid_t {.importc, header: "<unistd.h>".}

# File system functions for verification
proc access(pathname: cstring, mode: cint): cint {.importc, header: "<unistd.h>".}

const
  F_OK = 0  # Test for existence of file

proc getFileSize(filename: string): int64 =
  ## Gets the size of a file using system calls
  try:
    let fileInfo = getFileInfo(filename)
    return fileInfo.size
  except:
    return -1

proc checkImageSnapAvailable(): bool =
  ## Checks if imagesnap command is available on the system
  let result = system("which imagesnap > /dev/null 2>&1".cstring)
  return result == 0

proc installImageSnap(): bool =
  ## Attempts to install imagesnap using Homebrew
  echo "imagesnap not found. Attempting to install via Homebrew..."

  # Check if Homebrew is available
  let brewCheck = system("which brew > /dev/null 2>&1".cstring)
  if brewCheck != 0:
    echo "Error: Homebrew not found. Please install Homebrew first:"
    echo "  /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    return false

  # Install imagesnap
  echo "Installing imagesnap..."
  let installResult = system("brew install imagesnap".cstring)

  if installResult == 0:
    echo "imagesnap installed successfully!"
    return true
  else:
    echo "Error: Failed to install imagesnap"
    return false

proc takeWebcamPhoto(filename: string = "webcam_photo.jpg", device: string = ""): bool =
  ## Captures a photo from the webcam using imagesnap command
  echo "Capturing photo from webcam..."

  # Check if imagesnap is available
  if not checkImageSnapAvailable():
    echo "imagesnap command not found."
    if not installImageSnap():
      echo "Please install imagesnap manually:"
      echo "  brew install imagesnap"
      return false

  # Build the imagesnap command
  var command = "imagesnap"

  # Add device specification if provided
  if device != "":
    command.add(" -d \"" & device & "\"")

  # Add filename
  command.add(" " & filename)

  echo "Executing: ", command

  # Execute the command
  let result = system(command.cstring)

  if result == 0:
    # Verify the file was created
    if access(filename.cstring, F_OK) == 0:
      let fileSize = getFileSize(filename)
      if fileSize > 0:
        echo "Photo captured successfully!"
        echo "File: ", filename
        echo "Size: ", fileSize, " bytes"
        return true
      else:
        echo "Error: Photo file is empty"
        return false
    else:
      echo "Error: Photo file was not created"
      return false
  else:
    echo "Error: imagesnap command failed with exit code: ", result
    return false

proc listWebcamDevices(): seq[string] =
  ## Lists available webcam devices using imagesnap
  echo "Listing available webcam devices..."

  if not checkImageSnapAvailable():
    echo "imagesnap not available for device listing"
    return @[]

  # Note: This is a simplified version. In a real implementation,
  # we would capture the output of imagesnap -l and parse it
  echo "To list devices manually, run: imagesnap -l"
  return @["Built-in camera"]  # Default assumption

proc takeWebcamPhotoWithOptions(filename: string, warmup: int = 2, quiet: bool = true): bool =
  ## Captures a photo with additional options
  echo "Capturing webcam photo with options..."

  if not checkImageSnapAvailable():
    echo "imagesnap not available"
    return false

  var command = "imagesnap"

  # Add warmup time
  if warmup > 0:
    command.add(" -w " & $warmup)
    echo "Warmup time: ", warmup, " seconds"

  # Add quiet mode
  if quiet:
    command.add(" -q")

  command.add(" " & filename)
  echo "Executing: ", command

  let result = system(command.cstring)

  if result == 0:
    if access(filename.cstring, F_OK) == 0:
      let fileSize = getFileSize(filename)
      echo "Photo saved: ", filename, " (", fileSize, " bytes)"
      return true
    else:
      echo "Error: Photo file was not created"
      return false
  else:
    echo "Error: imagesnap failed with code: ", result
    return false

proc main() =
  echo "=== macOS Webcam Capture Demo ==="
  echo "Using direct system API calls"
  echo ""

  # Check system capabilities
  echo "System check:"
  echo "- Process ID: ", getpid()
  echo "- imagesnap available: ", checkImageSnapAvailable()

  # List available devices
  let devices = listWebcamDevices()
  echo "- Available devices: ", devices.len

  echo ""

  # Generate filename with timestamp
  let timestamp = now().format("yyyy-MM-dd-HH-mm-ss")
  let filename = "webcam_" & timestamp & ".jpg"

  echo "Demo 1: Basic webcam photo"
  if takeWebcamPhoto(filename):
    echo "✓ Basic webcam photo successful"
  else:
    echo "✗ Basic webcam photo failed"

  echo ""
  echo "Demo 2: Webcam photo with 3-second warmup"
  let warmupFilename = "webcam_warmup_" & timestamp & ".jpg"
  if takeWebcamPhotoWithOptions(warmupFilename, warmup = 3):
    echo "✓ Warmup webcam photo successful"
  else:
    echo "✗ Warmup webcam photo failed"

  echo ""
  echo "Technical details:"
  echo "- Used native imagesnap command via system() call"
  echo "- Direct file system verification with access() syscall"
  echo "- File size checking with stat() through getFileInfo"
  echo "- Process management with system calls"
  echo "- Automatic dependency installation via Homebrew"
  echo "- No framework dependencies - pure system calls"

when isMainModule:
  main()