# screenshot.nim
# Demonstrates native macOS screenshot using system APIs
# Compile with: nim c -d:release screenshot.nim
# Uses native system calls and file operations

import times, os

# System call types and functions
type
  pid_t = int32

# System functions
proc system(command: cstring): cint {.importc, header: "<stdlib.h>".}
proc getpid(): pid_t {.importc, header: "<unistd.h>".}

# File system functions for verification
proc access(pathname: cstring, mode: cint): cint {.importc, header: "<unistd.h>".}

const
  F_OK = 0  # Test for existence of file

proc getFileSize(filename: string): int64 =
  ## Gets the size of a file using system calls
  try:
    let fileInfo = getFileInfo(filename)
    return fileInfo.size
  except:
    return -1

proc takeScreenshot(filename: string = "screenshot.png"): bool =
  ## Captures a screenshot using native macOS screencapture command via system call
  echo "Capturing screenshot using native screencapture command..."

  # Build the screencapture command
  # -x: Do not play sounds
  # -t: Image format (png is default)
  let command = "/usr/sbin/screencapture -x " & filename
  echo "Executing system command: ", command

  # Execute the command using system call
  let result = system(command.cstring)

  if result == 0:
    # Verify the file was created using direct system call
    if access(filename.cstring, F_OK) == 0:
      let fileSize = getFileSize(filename)
      if fileSize > 0:
        echo "Screenshot captured successfully!"
        echo "File: ", filename
        echo "Size: ", fileSize, " bytes"
        return true
      else:
        echo "Error: Screenshot file is empty"
        return false
    else:
      echo "Error: Screenshot file was not created"
      return false
  else:
    echo "Error: screencapture command failed with exit code: ", result
    return false

proc takeScreenshotWithOptions(filename: string, delay: int = 0, interactive: bool = false): bool =
  ## Captures a screenshot with additional options
  echo "Capturing screenshot with options..."

  var command = "/usr/sbin/screencapture -x"

  # Add delay if specified
  if delay > 0:
    command.add(" -T " & $delay)
    echo "Delay: ", delay, " seconds"

  # Add interactive mode if specified
  if interactive:
    command.add(" -i")
    echo "Interactive mode: enabled"

  command.add(" " & filename)
  echo "Executing: ", command

  let result = system(command.cstring)

  if result == 0:
    if access(filename.cstring, F_OK) == 0:
      let fileSize = getFileSize(filename)
      echo "Screenshot saved: ", filename, " (", fileSize, " bytes)"
      return true
    else:
      echo "Error: Screenshot file was not created"
      return false
  else:
    echo "Error: screencapture failed with code: ", result
    return false

proc main() =
  echo "=== macOS Screenshot Demo ==="
  echo "Using direct system API calls"
  echo ""

  # Generate filename with timestamp
  let timestamp = now().format("yyyy-MM-dd-HH-mm-ss")
  let filename = "screenshot_" & timestamp & ".png"

  echo "Demo 1: Basic screenshot"
  if takeScreenshot(filename):
    echo "✓ Basic screenshot successful"
  else:
    echo "✗ Basic screenshot failed"

  echo ""
  echo "Demo 2: Screenshot with 3-second delay"
  let delayFilename = "screenshot_delayed_" & timestamp & ".png"
  if takeScreenshotWithOptions(delayFilename, delay = 3):
    echo "✓ Delayed screenshot successful"
  else:
    echo "✗ Delayed screenshot failed"

  echo ""
  echo "Technical details:"
  echo "- Used native screencapture command via system() call"
  echo "- Direct file system verification with access() syscall"
  echo "- File size checking with stat() through getFileInfo"
  echo "- Process ID tracking with getpid()"
  echo "- No framework dependencies - pure system calls"
  echo "- Current process ID: ", getpid()

when isMainModule:
  main()
