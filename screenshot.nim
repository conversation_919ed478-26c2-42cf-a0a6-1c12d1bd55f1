# screenshot.nim
# Demonstrates direct CoreGraphics API calls to capture desktop screenshots
# Compile with: nim c -d:release screenshot.nim
# Required frameworks: CoreGraphics, CoreFoundation, ImageIO

import strutils, times, os

# CoreGraphics and CoreFoundation types
type
  CGFloat = float64
  CGImageRef = pointer
  CGDataProviderRef = pointer
  CGColorSpaceRef = pointer
  CGContextRef = pointer
  CGDisplayID = uint32
  CGRect = object
    origin: CGPoint
    size: CGSize
  CGPoint = object
    x: CGFloat
    y: CGFloat
  CGSize = object
    width: CGFloat
    height: CGFloat
  CFStringRef = pointer
  CFURLRef = pointer
  CFTypeRef = pointer
  CFStringEncoding = uint32
  CGImageDestinationRef = pointer
  CFDictionaryRef = pointer

const
  kCFStringEncodingUTF8: CFStringEncoding = 0x08000100
  kCGMainDisplayID: CGDisplayID = 0

# Framework linking
{.passL: "-framework CoreGraphics".}
{.passL: "-framework CoreFoundation".}
{.passL: "-framework ImageIO".}

# CoreGraphics functions - using window capture instead of deprecated display capture
proc CGWindowListCreateImage(screenBounds: CGRect, listOption: uint32, windowID: uint32, imageOptions: uint32): CGImageRef {.importc, header: "<CoreGraphics/CoreGraphics.h>".}
proc CGImageGetWidth(image: CGImageRef): int {.importc, header: "<CoreGraphics/CoreGraphics.h>".}
proc CGImageGetHeight(image: CGImageRef): int {.importc, header: "<CoreGraphics/CoreGraphics.h>".}
proc CGRectInfinite(): CGRect {.importc, header: "<CoreGraphics/CoreGraphics.h>".}

# CoreFoundation functions
proc CFStringCreateWithCString(allocator: pointer, cStr: cstring, encoding: CFStringEncoding): CFStringRef {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFURLCreateWithFileSystemPath(allocator: pointer, filePath: CFStringRef, pathStyle: int, isDirectory: bool): CFURLRef {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFRelease(cf: CFTypeRef) {.importc, header: "<CoreFoundation/CoreFoundation.h>".}

# ImageIO functions
proc CGImageDestinationCreateWithURL(url: CFURLRef, imageType: CFStringRef, count: int, options: CFDictionaryRef): CGImageDestinationRef {.importc, header: "<ImageIO/ImageIO.h>".}
proc CGImageDestinationAddImage(idst: CGImageDestinationRef, image: CGImageRef, properties: CFDictionaryRef) {.importc, header: "<ImageIO/ImageIO.h>".}
proc CGImageDestinationFinalize(idst: CGImageDestinationRef): bool {.importc, header: "<ImageIO/ImageIO.h>".}

# Helper function to create CFString
proc createCFString(str: string): CFStringRef =
  return CFStringCreateWithCString(nil, str.cstring, kCFStringEncodingUTF8)

# Constants for window capture
const
  kCGWindowListOptionOnScreenOnly: uint32 = 1
  kCGWindowImageDefault: uint32 = 0
  kCFURLPOSIXPathStyle = 0

# Create our own UTType constants as strings
proc createPNGType(): CFStringRef = createCFString("public.png")
proc createJPEGType(): CFStringRef = createCFString("public.jpeg")

proc takeScreenshot(filename: string = "screenshot.png"): bool =
  ## Captures a screenshot of the desktop and saves it to a file
  echo "Capturing screenshot of desktop..."

  # Capture all on-screen windows (effectively the desktop)
  let screenBounds = CGRectInfinite()
  let image = CGWindowListCreateImage(screenBounds, kCGWindowListOptionOnScreenOnly, 0, kCGWindowImageDefault)
  if image == nil:
    echo "Error: Failed to capture screen image"
    return false

  # Get image dimensions
  let width = CGImageGetWidth(image)
  let height = CGImageGetHeight(image)
  echo "Captured image: ", width, "x", height, " pixels"

  # Create file URL
  let filePathCF = createCFString(filename)
  let fileURL = CFURLCreateWithFileSystemPath(nil, filePathCF, kCFURLPOSIXPathStyle, false)

  if fileURL == nil:
    echo "Error: Failed to create file URL"
    CFRelease(image)
    CFRelease(filePathCF)
    return false

  # Determine image type based on file extension
  let imageType = if filename.endsWith(".jpg") or filename.endsWith(".jpeg"):
    createJPEGType()
  else:
    createPNGType()

  # Create image destination
  let destination = CGImageDestinationCreateWithURL(fileURL, imageType, 1, nil)
  if destination == nil:
    echo "Error: Failed to create image destination"
    CFRelease(image)
    CFRelease(filePathCF)
    CFRelease(fileURL)
    return false

  # Add image to destination and finalize
  CGImageDestinationAddImage(destination, image, nil)
  let success = CGImageDestinationFinalize(destination)

  # Clean up
  CFRelease(image)
  CFRelease(filePathCF)
  CFRelease(fileURL)
  CFRelease(destination)
  CFRelease(imageType)

  if success:
    echo "Screenshot saved successfully to: ", filename
    return true
  else:
    echo "Error: Failed to save screenshot"
    return false

proc main() =
  echo "=== macOS Screenshot Demo ==="
  echo "Using direct CoreGraphics API calls"
  echo ""

  # Generate filename with timestamp
  let timestamp = now().format("yyyy-MM-dd-HH-mm-ss")
  let filename = "screenshot_" & timestamp & ".png"

  if takeScreenshot(filename):
    echo ""
    echo "Technical details:"
    echo "- Used CGWindowListCreateImage() for screen capture"
    echo "- Direct CoreGraphics and ImageIO APIs"
    echo "- PNG format with ImageIO framework"
    echo "- Manual memory management with CFRelease"
    echo "- Frameworks: CoreGraphics, CoreFoundation, ImageIO"
  else:
    echo "Screenshot capture failed!"

when isMainModule:
  main()
