# show_alert.nim
# Demonstrates direct C calls to show native macOS alert using CFUserNotification
# Compile with: nim c -d:release show_alert.nim
# Required frameworks: CoreFoundation

# CoreFoundation types and constants
type
  CFStringRef = pointer
  CFDictionaryRef = pointer
  CFUserNotificationRef = pointer
  CFOptionFlags = uint32
  CFTimeInterval = float64
  SInt32 = int32
  CFStringEncoding = uint32

const
  kCFStringEncodingUTF8: CFStringEncoding = 0x08000100
  kCFUserNotificationStopAlertLevel: CFOptionFlags = 0
  kCFUserNotificationDefaultResponse: SInt32 = 0

# Framework linking
{.passL: "-framework CoreFoundation".}

# CoreFoundation functions
proc CFStringCreateWithCString(allocator: pointer, cStr: cstring, encoding: CFStringEncoding): CFStringRef {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFRelease(cf: pointer) {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFDictionaryCreate(allocator: pointer, keys: ptr pointer, values: ptr pointer, numValues: int, keyCallBacks: pointer, valueCallBacks: pointer): CFDictionaryRef {.importc, header: "<CoreFoundation/CoreFoundation.h>".}

# CFUserNotification functions for showing alerts
proc CFUserNotificationCreate(allocator: pointer, timeout: CFTimeInterval, flags: CFOptionFlags, error: ptr SInt32, dictionary: CFDictionaryRef): CFUserNotificationRef {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFUserNotificationReceiveResponse(userNotification: CFUserNotificationRef, timeout: CFTimeInterval, responseFlags: ptr CFOptionFlags): SInt32 {.importc, header: "<CoreFoundation/CoreFoundation.h>".}

# CFUserNotification dictionary keys
var kCFUserNotificationAlertHeaderKey {.importc, header: "<CoreFoundation/CoreFoundation.h>".}: CFStringRef
var kCFUserNotificationAlertMessageKey {.importc, header: "<CoreFoundation/CoreFoundation.h>".}: CFStringRef
var kCFUserNotificationDefaultButtonTitleKey {.importc, header: "<CoreFoundation/CoreFoundation.h>".}: CFStringRef

# Helper functions
proc createCFString(str: string): CFStringRef =
  ## Creates a CFString from a Nim string
  return CFStringCreateWithCString(nil, str.cstring, kCFStringEncodingUTF8)

proc showAlert(title: string, message: string, buttonText: string = "OK"): int =
  ## Shows a native macOS alert dialog using CFUserNotification
  ## Returns 0 for success, non-zero for error

  # Create CFString objects for the alert
  let titleCF = createCFString(title)
  let messageCF = createCFString(message)
  let buttonCF = createCFString(buttonText)

  # Create dictionary with alert parameters
  var keys: array[3, pointer] = [
    cast[pointer](kCFUserNotificationAlertHeaderKey),
    cast[pointer](kCFUserNotificationAlertMessageKey),
    cast[pointer](kCFUserNotificationDefaultButtonTitleKey)
  ]
  var values: array[3, pointer] = [
    cast[pointer](titleCF),
    cast[pointer](messageCF),
    cast[pointer](buttonCF)
  ]

  let dict = CFDictionaryCreate(nil, cast[ptr pointer](addr keys[0]), cast[ptr pointer](addr values[0]), 3, nil, nil)

  # Create and show the notification
  var error: SInt32 = 0
  let notification = CFUserNotificationCreate(nil, 0.0, kCFUserNotificationStopAlertLevel, addr error, dict)

  var responseFlags: CFOptionFlags = 0
  let response = CFUserNotificationReceiveResponse(notification, 0.0, addr responseFlags)

  # Clean up
  CFRelease(titleCF)
  CFRelease(messageCF)
  CFRelease(buttonCF)
  CFRelease(dict)
  CFRelease(notification)

  return int(response)

proc main() =
  echo "=== macOS Native Alert Demo ==="
  echo "Using direct C calls to CoreFoundation framework"
  echo ""

  echo "Showing native macOS alert dialog..."

  let response = showAlert(
    "Nim FFI Demo",
    "This alert was created using direct C calls to CFUserNotification from Nim!\n\nNo high-level wrappers or bindings were used.",
    "Awesome!"
  )

  echo "Alert closed with response code: ", response
  echo ""
  echo "Technical details:"
  echo "- Used CFUserNotification C functions"
  echo "- Direct CoreFoundation string and dictionary creation"
  echo "- Manual memory management with CFRelease"
  echo "- Framework: CoreFoundation only"

when isMainModule:
  main()
