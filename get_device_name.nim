# get_device_name.nim
# Demonstrates direct FFI calls to CoreFoundation APIs to retrieve macOS device name
# Compile with: nim c -d:release get_device_name.nim
# Required frameworks: CoreFoundation

import strutils

# CoreFoundation types and constants
type
  CFTypeRef = pointer
  CFStringRef = CFTypeRef
  CFIndex = int
  CFStringEncoding = uint32

const
  kCFStringEncodingUTF8: CFStringEncoding = 0x08000100

# CoreFoundation function declarations
{.passL: "-framework CoreFoundation".}

proc CFStringCreateWithCString(allocator: pointer, cStr: cstring, encoding: CFStringEncoding): CFStringRef {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFStringGetCStringPtr(theString: CFStringRef, encoding: CFStringEncoding): cstring {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFStringGetCString(theString: CFStringRef, buffer: cstring, bufferSize: CFIndex, encoding: CFStringEncoding): bool {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFStringGetLength(theString: CFStringRef): CFIndex {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFRelease(cf: CFTypeRef) {.importc, header: "<CoreFoundation/CoreFoundation.h>".}

# SystemConfiguration framework for device name
{.passL: "-framework SystemConfiguration".}

proc SCDynamicStoreCopyComputerName(store: pointer, encoding: ptr CFStringEncoding): CFStringRef {.importc, header: "<SystemConfiguration/SystemConfiguration.h>".}

# Helper function to convert CFStringRef to Nim string
proc cfStringToNimString(cfStr: CFStringRef): string =
  if cfStr == nil:
    return ""

  # Try to get direct pointer first (more efficient)
  let directPtr = CFStringGetCStringPtr(cfStr, kCFStringEncodingUTF8)
  if directPtr != nil:
    return $directPtr

  # Fall back to copying into buffer
  let length = CFStringGetLength(cfStr)
  let bufferSize = length * 4 + 1  # UTF-8 can be up to 4 bytes per character
  let buffer = cast[cstring](alloc(bufferSize))

  if CFStringGetCString(cfStr, buffer, bufferSize, kCFStringEncodingUTF8):
    result = $buffer
  else:
    result = ""

  dealloc(buffer)

proc getDeviceName(): string =
  ## Retrieves the macOS device name using SystemConfiguration framework
  var encoding: CFStringEncoding
  let cfName = SCDynamicStoreCopyComputerName(nil, addr encoding)

  if cfName == nil:
    return "Unknown Device"

  result = cfStringToNimString(cfName)
  CFRelease(cfName)

proc main() =
  echo "=== macOS Device Name Retrieval Demo ==="
  echo "Using direct CoreFoundation and SystemConfiguration APIs"
  echo ""

  let deviceName = getDeviceName()
  echo "Device Name: ", deviceName

  # Additional system info for context
  echo "Architecture: ARM64 (Apple Silicon)"
  echo "Nim FFI: Direct C function calls"
  echo "Frameworks: CoreFoundation, SystemConfiguration"

when isMainModule:
  main()
