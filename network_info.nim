# network_info.nim
# Demonstrates direct BSD socket API calls to list network interfaces
# Compile with: nim c -d:release network_info.nim
# Uses getifaddrs() system call for network interface enumeration

import strutils

# Simplified network interface types
type
  ifaddrs = object
    ifa_next: ptr ifaddrs
    ifa_name: cstring
    ifa_flags: cuint
    ifa_addr: pointer
    ifa_netmask: pointer
    ifa_dstaddr: pointer
    ifa_data: pointer

# Interface flags
const
  IFF_UP = 0x1
  IFF_BROADCAST = 0x2
  IFF_LOOPBACK = 0x8
  IFF_POINTOPOINT = 0x10
  IFF_RUNNING = 0x40
  IFF_MULTICAST = 0x8000

# BSD socket functions
proc getifaddrs(ifap: ptr ptr ifaddrs): cint {.importc, header: "<ifaddrs.h>".}
proc freeifaddrs(ifa: ptr ifaddrs) {.importc, header: "<ifaddrs.h>".}

# Helper function to format interface flags
proc formatFlags(flags: cuint): string =
  var flagList: seq[string] = @[]

  if (flags and IFF_UP) != 0: flagList.add("UP")
  if (flags and IFF_RUNNING) != 0: flagList.add("RUNNING")
  if (flags and IFF_LOOPBACK) != 0: flagList.add("LOOPBACK")
  if (flags and IFF_BROADCAST) != 0: flagList.add("BROADCAST")
  if (flags and IFF_MULTICAST) != 0: flagList.add("MULTICAST")
  if (flags and IFF_POINTOPOINT) != 0: flagList.add("POINTOPOINT")

  return if flagList.len > 0: flagList.join(",") else: "NONE"

proc getNetworkInterfaces(): seq[tuple[name: string, flags: string]] =
  ## Gets network interface information using getifaddrs()
  var ifaddrs_ptr: ptr ifaddrs = nil

  if getifaddrs(addr ifaddrs_ptr) != 0:
    echo "Error: Could not get network interfaces"
    return @[]

  var current = ifaddrs_ptr
  var interfaces: seq[tuple[name: string, flags: string]] = @[]
  var seenInterfaces: seq[string] = @[]

  while current != nil:
    let name = $current.ifa_name

    # Only add each interface once
    if name notin seenInterfaces:
      let flags = formatFlags(current.ifa_flags)
      interfaces.add((name, flags))
      seenInterfaces.add(name)

    current = current.ifa_next

  freeifaddrs(ifaddrs_ptr)
  return interfaces

proc main() =
  echo "=== macOS Network Interfaces Demo ==="
  echo "Using direct BSD socket API calls"
  echo ""

  let interfaces = getNetworkInterfaces()

  if interfaces.len == 0:
    echo "No network interfaces found"
    return

  echo "Network Interfaces:"
  echo "==================="

  for iface in interfaces:
    echo "Interface: ", iface.name
    echo "  Flags: ", iface.flags
    echo ""

  echo "Technical details:"
  echo "- Used getifaddrs() BSD system call"
  echo "- Direct interface flag parsing"
  echo "- Manual memory management with freeifaddrs()"
  echo "- No framework dependencies"

when isMainModule:
  main()
