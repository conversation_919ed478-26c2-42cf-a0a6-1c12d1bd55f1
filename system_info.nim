# system_info.nim
# Demonstrates direct system calls to retrieve macOS version and hardware info
# Compile with: nim c -d:release system_info.nim
# Required frameworks: CoreFoundation, SystemConfiguration

import strutils

# CoreFoundation types and constants
type
  CFStringRef = pointer
  CFDictionaryRef = pointer
  CFTypeRef = pointer
  CFIndex = int
  CFStringEncoding = uint32

const
  kCFStringEncodingUTF8: CFStringEncoding = 0x08000100

# Framework linking
{.passL: "-framework CoreFoundation".}
{.passL: "-framework SystemConfiguration".}

# CoreFoundation functions
proc CFStringCreateWithCString(allocator: pointer, cStr: cstring, encoding: CFStringEncoding): CFStringRef {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFStringGetCStringPtr(theString: CFStringRef, encoding: CFStringEncoding): cstring {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFStringGetCString(theString: CFStringRef, buffer: cstring, bufferSize: CFIndex, encoding: CFStringEncoding): bool {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFStringGetLength(theString: CFStringRef): CFIndex {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFRelease(cf: CFTypeRef) {.importc, header: "<CoreFoundation/CoreFoundation.h>".}
proc CFDictionaryGetValue(theDict: CFDictionaryRef, key: pointer): pointer {.importc, header: "<CoreFoundation/CoreFoundation.h>".}

# SystemConfiguration functions
proc SCDynamicStoreCopyComputerName(store: pointer, encoding: ptr CFStringEncoding): CFStringRef {.importc, header: "<SystemConfiguration/SystemConfiguration.h>".}

# System calls for uname
type
  Utsname = object
    sysname: array[256, char]
    nodename: array[256, char]
    release: array[256, char]
    version: array[256, char]
    machine: array[256, char]

proc uname(buf: ptr Utsname): cint {.importc, header: "<sys/utsname.h>".}

# sysctl for hardware info
proc sysctlbyname(name: cstring, oldp: pointer, oldlenp: ptr int, newp: pointer, newlen: int): cint {.importc, header: "<sys/sysctl.h>".}

# Helper function to convert CFStringRef to Nim string
proc cfStringToNimString(cfStr: CFStringRef): string =
  if cfStr == nil:
    return ""

  # Try to get direct pointer first (more efficient)
  let directPtr = CFStringGetCStringPtr(cfStr, kCFStringEncodingUTF8)
  if directPtr != nil:
    return $directPtr

  # Fall back to copying into buffer
  let length = CFStringGetLength(cfStr)
  let bufferSize = length * 4 + 1  # UTF-8 can be up to 4 bytes per character
  let buffer = cast[cstring](alloc(bufferSize))

  if CFStringGetCString(cfStr, buffer, bufferSize, kCFStringEncodingUTF8):
    result = $buffer
  else:
    result = ""

  dealloc(buffer)

proc getSystemInfo(): tuple[computerName: string, osVersion: string, kernelVersion: string, architecture: string, cpuBrand: string, memorySize: string] =
  ## Retrieves comprehensive system information using native APIs

  # Get computer name
  var encoding: CFStringEncoding
  let cfName = SCDynamicStoreCopyComputerName(nil, addr encoding)
  let computerName = if cfName != nil: cfStringToNimString(cfName) else: "Unknown"
  if cfName != nil: CFRelease(cfName)

  # Get system info using uname
  var unameInfo: Utsname
  if uname(addr unameInfo) == 0:
    let osVersion = $cast[cstring](addr unameInfo.release[0])
    let kernelVersion = $cast[cstring](addr unameInfo.version[0])
    let architecture = $cast[cstring](addr unameInfo.machine[0])

    # Get CPU brand using sysctl
    var cpuBrandSize: int = 256
    let cpuBrandBuffer = alloc(cpuBrandSize)
    var cpuBrand = "Unknown"
    if sysctlbyname("machdep.cpu.brand_string", cpuBrandBuffer, addr cpuBrandSize, nil, 0) == 0:
      cpuBrand = $cast[cstring](cpuBrandBuffer)
    dealloc(cpuBrandBuffer)

    # Get memory size using sysctl
    var memSize: uint64
    var memSizeLen = sizeof(uint64)
    var memorySize = "Unknown"
    if sysctlbyname("hw.memsize", addr memSize, addr memSizeLen, nil, 0) == 0:
      let memGB = memSize div (1024 * 1024 * 1024)
      memorySize = $memGB & " GB"

    return (computerName, osVersion, kernelVersion, architecture, cpuBrand, memorySize)
  else:
    return (computerName, "Unknown", "Unknown", "Unknown", "Unknown", "Unknown")

proc main() =
  echo "=== macOS System Information Demo ==="
  echo "Using direct system calls and native APIs"
  echo ""

  let sysInfo = getSystemInfo()

  echo "Computer Name: ", sysInfo.computerName
  echo "OS Version: ", sysInfo.osVersion
  echo "Kernel Version: ", sysInfo.kernelVersion
  echo "Architecture: ", sysInfo.architecture
  echo "CPU: ", sysInfo.cpuBrand
  echo "Memory: ", sysInfo.memorySize
  echo ""
  echo "Technical details:"
  echo "- Used SystemConfiguration for computer name"
  echo "- Used uname() system call for OS info"
  echo "- Used sysctl() for hardware details"
  echo "- Direct C function calls via FFI"
  echo "- Frameworks: CoreFoundation, SystemConfiguration"

when isMainModule:
  main()
